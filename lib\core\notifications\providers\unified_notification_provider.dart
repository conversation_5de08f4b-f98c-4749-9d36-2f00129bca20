import 'dart:async';
import 'dart:convert';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../utils/logger.dart';
import '../models/notification_analytics.dart';
import '../models/notification_channel.dart';
import '../models/notification_payload.dart';
import '../models/scheduled_notification.dart';
import '../services/background_sync_notification_service.dart';
import '../services/notification_analytics_service.dart';
import '../services/notification_channel_manager.dart';
import '../services/notification_scheduler.dart';
import '../services/notification_service.dart';
import '../services/prayer_notification_service.dart';
import '../services/system_alert_notification_service.dart';
import 'prayer_notification_provider.dart';

part 'unified_notification_provider.g.dart';

/// Service Health Status
///
/// Represents the health status of a notification service
enum ServiceHealthStatus { healthy, degraded, unhealthy, unknown }

/// Notification Service Factory
///
/// Abstract factory for creating notification service instances
/// following Context7 MCP factory pattern best practices.
abstract class NotificationServiceFactory {
  /// Create a notification service instance
  Future<dynamic> createService();

  /// Validate service configuration
  bool validateConfiguration();

  /// Get service type identifier
  String get serviceType;
}

/// Prayer Notification Service Factory
///
/// Factory for creating prayer notification service instances
class PrayerNotificationServiceFactory extends NotificationServiceFactory {
  final NotificationService notificationService;

  PrayerNotificationServiceFactory({required this.notificationService});

  @override
  Future<PrayerNotificationService> createService() async {
    // Note: PrayerNotificationService requires prayerTimesService parameter
    // This is a placeholder - actual implementation would need proper dependency injection
    throw UnimplementedError('PrayerNotificationService creation requires prayerTimesService dependency');
  }

  @override
  bool validateConfiguration() => notificationService != null;

  @override
  String get serviceType => 'prayer_notification';
}

/// Sync Notification Service Factory
///
/// Factory for creating background sync notification service instances
class SyncNotificationServiceFactory extends NotificationServiceFactory {
  final NotificationService notificationService;

  SyncNotificationServiceFactory({required this.notificationService});

  @override
  Future<BackgroundSyncNotificationService> createService() async {
    // Note: BackgroundSyncNotificationService requires progressTrackingService parameter
    // This is a placeholder - actual implementation would need proper dependency injection
    throw UnimplementedError('BackgroundSyncNotificationService creation requires progressTrackingService dependency');
  }

  @override
  bool validateConfiguration() => notificationService != null;

  @override
  String get serviceType => 'sync_notification';
}

/// System Alert Service Factory
///
/// Factory for creating system alert notification service instances
class SystemAlertServiceFactory extends NotificationServiceFactory {
  final NotificationService notificationService;

  SystemAlertServiceFactory({required this.notificationService});

  @override
  Future<SystemAlertNotificationService> createService() async {
    final service = SystemAlertNotificationService(notificationService: notificationService);
    await service.initialize();
    return service;
  }

  @override
  bool validateConfiguration() => notificationService != null;

  @override
  String get serviceType => 'system_alert';
}

/// Notification Fallback Strategy
///
/// Abstract strategy for handling notification service failures
/// following Context7 MCP strategy pattern best practices.
abstract class NotificationFallbackStrategy {
  /// Handle service failure
  Future<bool> handleFailure(String serviceName, Exception error);

  /// Get fallback service if available
  Future<dynamic> getFallbackService();

  /// Check if fallback is available
  bool get hasFallback;

  /// Get strategy name
  String get strategyName;
}

/// Basic Fallback Strategy
///
/// Basic fallback strategy that logs errors and provides minimal functionality
class BasicFallbackStrategy extends NotificationFallbackStrategy {
  @override
  Future<bool> handleFailure(String serviceName, Exception error) async {
    AppLogger.error('Service $serviceName failed, using basic fallback', error);
    return true;
  }

  @override
  Future<dynamic> getFallbackService() async {
    // Return a basic notification service that just logs
    return null;
  }

  @override
  bool get hasFallback => false;

  @override
  String get strategyName => 'basic_fallback';
}

/// Unified Notification Manager State
///
/// Represents the complete state of the unified notification system
/// following Context7 MCP single source of truth principle.
class UnifiedNotificationState {
  final bool isInitialized;
  final bool isEnabled;
  final Map<String, bool> serviceStatus;
  final List<ScheduledNotificationInfo> pendingNotifications;
  final NotificationAnalytics analytics;
  final DateTime lastUpdate;
  final String? error;

  const UnifiedNotificationState({
    required this.isInitialized,
    required this.isEnabled,
    required this.serviceStatus,
    required this.pendingNotifications,
    required this.analytics,
    required this.lastUpdate,
    this.error,
  });

  factory UnifiedNotificationState.initial() {
    return UnifiedNotificationState(
      isInitialized: false,
      isEnabled: false,
      serviceStatus: const {},
      pendingNotifications: const [],
      analytics: NotificationAnalytics.initial(),
      lastUpdate: DateTime.now(),
    );
  }

  UnifiedNotificationState copyWith({
    bool? isInitialized,
    bool? isEnabled,
    Map<String, bool>? serviceStatus,
    List<ScheduledNotificationInfo>? pendingNotifications,
    NotificationAnalytics? analytics,
    DateTime? lastUpdate,
    String? error,
  }) {
    return UnifiedNotificationState(
      isInitialized: isInitialized ?? this.isInitialized,
      isEnabled: isEnabled ?? this.isEnabled,
      serviceStatus: serviceStatus ?? this.serviceStatus,
      pendingNotifications: pendingNotifications ?? this.pendingNotifications,
      analytics: analytics ?? this.analytics,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error ?? this.error,
    );
  }
}

/// Unified Notification Request
///
/// Standardized request format for all notification types
/// following Context7 MCP interface segregation principle.
class UnifiedNotificationRequest {
  final int id;
  final String title;
  final String body;
  final NotificationType type;
  final DateTime? scheduledDate;
  final Map<String, dynamic>? payload;
  final NotificationPriority priority;
  final String? channelKey;
  final bool allowWhileIdle;
  final Duration? timeout;

  const UnifiedNotificationRequest({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.scheduledDate,
    this.payload,
    this.priority = NotificationPriority.normal,
    this.channelKey,
    this.allowWhileIdle = true,
    this.timeout,
  });
}

/// Notification Types
///
/// Enumeration of all supported notification types
/// following Context7 MCP explicit interface principle.
enum NotificationType { prayer, sync, systemAlert, analytics, custom }

/// Notification Priority
///
/// Priority levels for notification scheduling and display
/// following Context7 MCP priority-based resource allocation.
enum NotificationPriority { low, normal, high, critical }

/// Unified Notification Manager Provider
///
/// Single source of truth for all notification operations
/// following Context7 MCP consolidation best practices.
///
/// **Key Features:**
/// - Unified interface for all notification types
/// - Automatic service lifecycle management
/// - Comprehensive error handling and recovery
/// - Performance monitoring and analytics
/// - Resource-efficient operation batching
/// - Service factory methods for different notification types
/// - Dependency injection with proper service composition
/// - Fallback strategies for service failures
///
/// **Architecture Principles:**
/// - Single Responsibility: Manages all notification operations
/// - Open/Closed: Extensible for new notification types
/// - Dependency Inversion: Depends on abstractions, not concretions
/// - Interface Segregation: Clean, focused API surface
/// - Factory Pattern: Service creation and configuration
/// - Strategy Pattern: Different notification handling strategies
@riverpod
class UnifiedNotificationManager extends _$UnifiedNotificationManager {
  // Core services - injected via dependency injection
  NotificationService? _notificationService;
  PrayerNotificationService? _prayerService;
  BackgroundSyncNotificationService? _syncService;
  SystemAlertNotificationService? _alertService;
  NotificationChannelManager? _channelManager;
  NotificationScheduler? _scheduler;
  NotificationAnalyticsService? _analyticsService;

  // Service factory instances for different notification types
  late final Map<NotificationType, NotificationServiceFactory> _serviceFactories;

  // Fallback services for error recovery
  late final Map<NotificationType, NotificationFallbackStrategy> _fallbackStrategies;

  // Internal state management
  final Map<int, Timer> _scheduledTimers = {};
  final List<UnifiedNotificationRequest> _pendingRequests = [];
  Timer? _batchProcessingTimer;
  bool _isDisposed = false;

  // Service health monitoring
  final Map<String, ServiceHealthStatus> _serviceHealthStatus = {};
  Timer? _healthCheckTimer;

  /// Initialize the Unified Notification Manager
  ///
  /// This method sets up all notification services, configures dependencies,
  /// and establishes the unified interface following Context7 MCP best practices.
  ///
  /// **Initialization Process:**
  /// 1. Service dependency injection and configuration
  /// 2. Service factory setup for different notification types
  /// 3. Fallback strategy configuration for error recovery
  /// 4. Health monitoring setup for service status tracking
  /// 5. Performance analytics initialization
  /// 6. Resource cleanup and disposal handling
  ///
  /// **Returns:** Initial state with all services configured
  @override
  Future<UnifiedNotificationState> build() async {
    AppLogger.info('🚀 Initializing Unified Notification Manager...');

    try {
      // Initialize core services with dependency injection
      await _initializeServices();

      // Set up service factories for different notification types
      _initializeServiceFactories();

      // Configure fallback strategies for error recovery
      _initializeFallbackStrategies();

      // Set up batch processing
      _setupBatchProcessing();

      // Set up health monitoring for all services
      _setupHealthMonitoring();

      // Set up disposal handling
      ref.onDispose(_dispose);

      // Create initial state with successful initialization
      final initialState = UnifiedNotificationState(
        isInitialized: true,
        isEnabled: true,
        error: null,
        lastUpdate: DateTime.now(),
        serviceStatus: {
          'notification_service': true,
          'prayer_service': true,
          'sync_service': true,
          'alert_service': true,
          'channel_manager': true,
          'scheduler': true,
          'analytics_service': true,
        },
        pendingNotifications: [],
        analytics: NotificationAnalytics.initial(),
      );

      AppLogger.info('✅ Unified Notification Manager initialized successfully');
      return initialState;
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize Unified Notification Manager', e, stackTrace);

      return UnifiedNotificationState(
        isInitialized: false,
        isEnabled: false,
        error: 'Initialization failed: ${e.toString()}',
        lastUpdate: DateTime.now(),
        serviceStatus: {},
        pendingNotifications: [],
        analytics: NotificationAnalytics.initial(),
      );
    }
  }

  /// Initialize core notification services with dependency injection
  ///
  /// Sets up all required services following Context7 MCP dependency injection pattern.
  Future<void> _initializeServices() async {
    AppLogger.debug('🔧 Initializing core notification services...');

    try {
      // Initialize core notification service
      _notificationService = ref.read(notificationServiceProvider);
      await _notificationService!.initialize();

      // Initialize prayer notification service
      _prayerService = ref.read(prayerNotificationServiceProvider);
      await _prayerService!.initialize();

      // Initialize background sync service
      _syncService = ref.read(backgroundSyncNotificationServiceProvider);
      await _syncService!.initialize();

      // Initialize system alert service
      _alertService = ref.read(systemAlertNotificationServiceProvider);
      await _alertService!.initialize();

      // Initialize channel manager
      _channelManager = ref.read(notificationChannelManagerProvider);
      await _channelManager!.initialize();

      // Initialize scheduler
      _scheduler = ref.read(notificationSchedulerProvider);
      await _scheduler!.initialize();

      // Initialize analytics service
      _analyticsService = ref.read(notificationAnalyticsServiceProvider);
      await _analyticsService!.initialize();

      AppLogger.debug('✅ Core notification services initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize core services', e, stackTrace);
      rethrow;
    }
  }

  /// Initialize service factories for different notification types
  ///
  /// Sets up factory instances following Context7 MCP factory pattern.
  void _initializeServiceFactories() {
    AppLogger.debug('🏭 Initializing service factories');

    _serviceFactories = {
      NotificationType.prayer: PrayerNotificationServiceFactory(notificationService: _notificationService!),
      NotificationType.sync: SyncNotificationServiceFactory(notificationService: _notificationService!),
      NotificationType.systemAlert: SystemAlertServiceFactory(notificationService: _notificationService!),
    };

    AppLogger.debug('✅ Service factories initialized');
  }

  /// Initialize fallback strategies for error recovery
  ///
  /// Sets up fallback strategies following Context7 MCP strategy pattern.
  void _initializeFallbackStrategies() {
    AppLogger.debug('🛡️ Initializing fallback strategies');

    _fallbackStrategies = {
      NotificationType.prayer: BasicFallbackStrategy(),
      NotificationType.sync: BasicFallbackStrategy(),
      NotificationType.systemAlert: BasicFallbackStrategy(),
      NotificationType.analytics: BasicFallbackStrategy(),
      NotificationType.custom: BasicFallbackStrategy(),
    };

    AppLogger.debug('✅ Fallback strategies initialized');
  }

  /// Set up health monitoring for all services
  ///
  /// Implements service health monitoring following Context7 MCP monitoring patterns.
  void _setupHealthMonitoring() {
    AppLogger.debug('🏥 Setting up health monitoring');

    _healthCheckTimer = Timer.periodic(const Duration(minutes: 5), (_) => _performHealthCheck());

    AppLogger.debug('✅ Health monitoring setup complete');
  }

  /// Perform health check on all services
  ///
  /// Checks the health status of all notification services.
  Future<void> _performHealthCheck() async {
    try {
      AppLogger.debug('🔍 Performing service health check');

      final healthStatuses = <String, ServiceHealthStatus>{};

      // Check core services
      healthStatuses['notification_service'] = _checkServiceHealth(_notificationService);
      healthStatuses['prayer_service'] = _checkServiceHealth(_prayerService);
      healthStatuses['sync_service'] = _checkServiceHealth(_syncService);
      healthStatuses['alert_service'] = _checkServiceHealth(_alertService);
      healthStatuses['channel_manager'] = _checkServiceHealth(_channelManager);
      healthStatuses['scheduler'] = _checkServiceHealth(_scheduler);
      healthStatuses['analytics_service'] = _checkServiceHealth(_analyticsService);

      // Update service health status
      _serviceHealthStatus.clear();
      _serviceHealthStatus.addAll(healthStatuses);

      // Log any unhealthy services
      final unhealthyServices = healthStatuses.entries
          .where((entry) => entry.value != ServiceHealthStatus.healthy)
          .map((entry) => entry.key)
          .toList();

      if (unhealthyServices.isNotEmpty) {
        AppLogger.warning('⚠️ Unhealthy services detected: ${unhealthyServices.join(', ')}');
      }

      AppLogger.debug('✅ Health check completed');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Health check failed', e, stackTrace);
    }
  }

  /// Check the health status of a service
  ///
  /// Returns the health status of a given service instance.
  ServiceHealthStatus _checkServiceHealth(dynamic service) {
    if (service == null) return ServiceHealthStatus.unhealthy;

    try {
      // Basic health check - service exists (we assume if it's not null, it's healthy)
      // In a real implementation, we could add more sophisticated health checks
      if (service is NotificationService ||
          service is PrayerNotificationService ||
          service is BackgroundSyncNotificationService ||
          service is SystemAlertNotificationService ||
          service is NotificationChannelManager ||
          service is NotificationScheduler ||
          service is NotificationAnalyticsService) {
        return ServiceHealthStatus.healthy;
      }

      return ServiceHealthStatus.unknown;
    } catch (e) {
      return ServiceHealthStatus.unhealthy;
    }
  }

  /// Initialize all notification services asynchronously
  ///
  /// Follows Context7 MCP initialization patterns with proper
  /// error handling and service dependency management.
  Future<void> _initializeAsync() async {
    try {
      AppLogger.info('🔧 Starting notification services initialization');

      // Initialize core services in dependency order
      await _initializeCoreServices();
      await _initializeSpecializedServices();
      await _initializeChannels();
      await _requestPermissions();

      // Initialize service factories after services are created
      _initializeServiceFactories();

      // Setup service lifecycle management following Context7 MCP patterns
      _setupServiceLifecycleManagement();

      // Update state to reflect successful initialization
      final currentState = state.value;
      if (currentState != null) {
        state = AsyncValue.data(
          currentState.copyWith(
            isInitialized: true,
            isEnabled: true,
            serviceStatus: {
              'notification_service': _notificationService != null,
              'prayer_service': _prayerService != null,
              'sync_service': _syncService != null,
              'alert_service': _alertService != null,
              'channel_manager': _channelManager != null,
              'scheduler': _scheduler != null,
              'analytics_service': _analyticsService != null,
            },
            lastUpdate: DateTime.now(),
          ),
        );
      }

      AppLogger.info('✅ Unified Notification Manager initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize Unified Notification Manager', e, stackTrace);

      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Setup service lifecycle management
  ///
  /// Implements comprehensive service lifecycle management following Context7 MCP
  /// best practices with proper disposal patterns and resource cleanup.
  void _setupServiceLifecycleManagement() {
    AppLogger.info('🔄 Setting up service lifecycle management');

    // Register disposal callbacks for all services following Context7 MCP patterns
    ref.onDispose(() async {
      AppLogger.info('🧹 Starting unified notification service disposal');
      await _disposeAllServices();
      AppLogger.info('✅ Unified notification service disposal completed');
    });

    // Setup individual service lifecycle monitoring
    _setupIndividualServiceLifecycles();

    // Setup resource cleanup monitoring
    _setupResourceCleanupMonitoring();

    AppLogger.info('✅ Service lifecycle management setup completed');
  }

  /// Setup individual service lifecycles
  ///
  /// Configures lifecycle management for each individual service
  /// following Context7 MCP dependency inversion principles.
  void _setupIndividualServiceLifecycles() {
    // Monitor notification service lifecycle
    if (_notificationService != null) {
      _monitorServiceHealth('notification_service', () => _notificationService != null);
    }

    // Monitor prayer service lifecycle
    if (_prayerService != null) {
      _monitorServiceHealth('prayer_service', () => _prayerService != null);
    }

    // Monitor sync service lifecycle
    if (_syncService != null) {
      _monitorServiceHealth('sync_service', () => _syncService != null);
    }

    // Monitor alert service lifecycle
    if (_alertService != null) {
      _monitorServiceHealth('alert_service', () => _alertService != null);
    }

    // Monitor channel manager lifecycle
    if (_channelManager != null) {
      _monitorServiceHealth('channel_manager', () => _channelManager != null);
    }

    // Monitor scheduler lifecycle
    if (_scheduler != null) {
      _monitorServiceHealth('scheduler', () => _scheduler != null);
    }

    // Monitor analytics service lifecycle
    if (_analyticsService != null) {
      _monitorServiceHealth('analytics_service', () => _analyticsService != null);
    }
  }

  /// Setup resource cleanup monitoring
  ///
  /// Implements resource cleanup monitoring following Context7 MCP patterns
  /// for memory management and resource optimization.
  void _setupResourceCleanupMonitoring() {
    // Setup periodic resource cleanup
    Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      _performResourceCleanup();
    });

    // Register cleanup on disposal
    ref.onDispose(() {
      _performResourceCleanup();
    });
  }

  /// Initialize core notification services
  ///
  /// Sets up the foundational services required by all notification types.
  Future<void> _initializeCoreServices() async {
    AppLogger.debug('🔧 Initializing core notification services');

    // Initialize base notification service
    _notificationService = ref.read(notificationServiceProvider);
    await _notificationService!.initialize();

    // Initialize channel manager
    _channelManager = ref.read(notificationChannelManagerProvider);
    await _channelManager!.initialize();

    // Initialize scheduler
    _scheduler = ref.read(notificationSchedulerProvider);
    await _scheduler!.initialize();

    AppLogger.debug('✅ Core notification services initialized');
  }

  /// Initialize specialized notification services
  ///
  /// Sets up domain-specific services for different notification types.
  Future<void> _initializeSpecializedServices() async {
    AppLogger.debug('🔧 Initializing specialized notification services');

    // Initialize prayer notification service
    _prayerService = ref.read(prayerNotificationServiceProvider);
    await _prayerService!.initialize();

    // Initialize sync notification service
    _syncService = ref.read(backgroundSyncNotificationServiceProvider);
    await _syncService!.initialize();

    // Initialize system alert service
    _alertService = ref.read(systemAlertNotificationServiceProvider);
    await _alertService!.initialize();

    // Initialize analytics service
    _analyticsService = ref.read(notificationAnalyticsServiceProvider);
    await _analyticsService!.initialize();

    AppLogger.debug('✅ Specialized notification services initialized');
  }

  /// Initialize notification channels
  ///
  /// Sets up all required notification channels for different types.
  Future<void> _initializeChannels() async {
    AppLogger.debug('🔧 Initializing notification channels');

    if (_channelManager == null) {
      throw StateError('Channel manager not initialized');
    }

    // Create channels for all notification types
    await _channelManager!.createChannel(NotificationChannelKey.prayerTimes);
    await _channelManager!.createChannel(NotificationChannelKey.backgroundSync);
    await _channelManager!.createChannel(NotificationChannelKey.systemAlerts);
    await _channelManager!.createChannel(NotificationChannelKey.general);

    AppLogger.debug('✅ Notification channels initialized');
  }

  /// Request all required permissions
  ///
  /// Handles permission requests for all notification types with proper fallbacks.
  /// Note: Permissions are handled automatically during service initialization.
  Future<void> _requestPermissions() async {
    AppLogger.debug('🔐 Notification permissions handled during service initialization');

    // Permissions are automatically requested during NotificationService.initialize()
    // No additional action needed here as services handle their own permissions

    AppLogger.debug('✅ Notification permissions handled');
  }

  /// Set up batch processing for notification requests
  ///
  /// Implements efficient batching to reduce system overhead.
  void _setupBatchProcessing() {
    _batchProcessingTimer = Timer.periodic(const Duration(milliseconds: 100), (_) => _processPendingRequests());
  }

  /// Process pending notification requests in batches
  ///
  /// Optimizes performance by batching multiple requests together.
  void _processPendingRequests() {
    final currentState = state.value;
    if (_pendingRequests.isEmpty || currentState == null || !currentState.isInitialized) return;

    final requestsToProcess = List<UnifiedNotificationRequest>.from(_pendingRequests);
    _pendingRequests.clear();

    for (final request in requestsToProcess) {
      _processNotificationRequest(request);
    }
  }

  /// Process individual notification request
  ///
  /// Routes requests to appropriate service based on type.
  Future<void> _processNotificationRequest(UnifiedNotificationRequest request) async {
    try {
      AppLogger.debug('📤 Processing notification request: ${request.type}');

      switch (request.type) {
        case NotificationType.prayer:
          await _processPrayerNotification(request);
          break;
        case NotificationType.sync:
          await _processSyncNotification(request);
          break;
        case NotificationType.systemAlert:
          await _processSystemAlertNotification(request);
          break;
        case NotificationType.analytics:
          await _processAnalyticsNotification(request);
          break;
        case NotificationType.custom:
          await _processCustomNotification(request);
          break;
      }

      // Track successful processing
      _analyticsService?.trackNotificationDelivered(
        notificationId: request.id.toString(),
        channelKey: _getChannelKeyFromType(request.type),
        deliveryTime: DateTime.now(),
      );
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to process notification request', e, stackTrace);
      await _analyticsService?.trackNotificationError(
        errorType: 'notification_processing_error',
        errorMessage: e.toString(),
        notificationId: request.id.toString(),
        channelKey: _getChannelKeyFromType(request.type),
        metadata: request.payload,
      );
    }
  }

  /// Process prayer notification request
  ///
  /// Handles prayer-specific notification logic with proper validation.
  Future<void> _processPrayerNotification(UnifiedNotificationRequest request) async {
    if (_prayerService == null) {
      throw StateError('Prayer notification service not initialized');
    }

    // Use the prayer service to schedule daily notifications
    // Note: PrayerNotificationService uses scheduleDailyPrayerNotifications
    // For individual prayer notifications, we delegate to the core notification service
    await _notificationService!.scheduleNotification(
      id: request.id,
      title: request.title,
      body: request.body,
      scheduledDate: request.scheduledDate!,
      channelKey: NotificationChannelKey.prayerTimes,
      payload: request.payload != null ? jsonEncode(request.payload) : null,
    );
  }

  /// Process sync notification request
  ///
  /// Handles background sync notification logic.
  Future<void> _processSyncNotification(UnifiedNotificationRequest request) async {
    if (_syncService == null) {
      throw StateError('Sync notification service not initialized');
    }

    await _syncService!.showSyncStartNotification(
      operationId: request.id.toString(),
      title: request.title,
      description: request.body,
    );
  }

  /// Process system alert notification request
  ///
  /// Handles system alert notification logic with priority handling.
  Future<void> _processSystemAlertNotification(UnifiedNotificationRequest request) async {
    if (_alertService == null) {
      throw StateError('System alert service not initialized');
    }

    await _alertService!.showCriticalAlert(title: request.title, message: request.body, metadata: request.payload);
  }

  /// Process analytics notification request
  ///
  /// Handles analytics-related notifications.
  Future<void> _processAnalyticsNotification(UnifiedNotificationRequest request) async {
    if (_analyticsService == null) {
      throw StateError('Analytics service not initialized');
    }

    // Analytics notifications are typically internal - track as performance event
    await _analyticsService!.trackNotificationPerformance(
      operationType: request.title,
      processingTime: Duration.zero,
      notificationId: request.id.toString(),
      metadata: request.payload,
    );
  }

  /// Process custom notification request
  ///
  /// Handles custom notification types with flexible configuration.
  Future<void> _processCustomNotification(UnifiedNotificationRequest request) async {
    if (_notificationService == null) {
      throw StateError('Notification service not initialized');
    }

    await _notificationService!.showNotification(
      id: request.id,
      title: request.title,
      body: request.body,
      channelKey: NotificationChannelKey.general,
      payload: request.payload != null ? jsonEncode(request.payload) : null,
    );
  }

  // ========================================
  // PUBLIC API METHODS
  // ========================================

  /// Schedule a notification
  ///
  /// Main entry point for scheduling notifications of any type.
  /// Follows Context7 MCP unified interface principle.
  Future<void> scheduleNotification(UnifiedNotificationRequest request) async {
    if (_isDisposed) {
      AppLogger.warning('⚠️ Attempted to schedule notification on disposed manager');
      return;
    }

    // Validate request
    final validationResult = _validateRequest(request);
    if (!validationResult.isValid) {
      throw ArgumentError('Invalid notification request: ${validationResult.errors.join(', ')}');
    }

    // Add to pending requests for batch processing
    _pendingRequests.add(request);

    AppLogger.debug('📝 Notification request queued: ${request.id}');
  }

  /// Cancel a specific notification
  ///
  /// Cancels a notification by ID across all services.
  Future<void> cancelNotification(int id) async {
    if (_isDisposed) return;

    try {
      AppLogger.debug('🗑️ Cancelling notification: $id');

      // Cancel from all services
      await _notificationService?.cancelNotification(id);
      // Note: Other services don't have individual cancel methods
      // They use the core notification service for cancellation

      // Cancel any scheduled timers
      _scheduledTimers[id]?.cancel();
      _scheduledTimers.remove(id);

      // Update pending notifications list
      final currentState = state.value;
      if (currentState != null) {
        final updatedPending = currentState.pendingNotifications
            .where((notification) => notification.id != id)
            .toList();
        state = AsyncValue.data(
          currentState.copyWith(pendingNotifications: updatedPending, lastUpdate: DateTime.now()),
        );
      }

      AppLogger.debug('✅ Notification cancelled: $id');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to cancel notification: $id', e, stackTrace);
    }
  }

  /// Cancel all notifications
  ///
  /// Cancels all pending and active notifications across all services.
  Future<void> cancelAllNotifications() async {
    if (_isDisposed) return;

    try {
      AppLogger.info('🗑️ Cancelling all notifications');

      // Cancel from all services
      await _notificationService?.cancelAllNotifications();
      // Note: Other services don't have individual cancel all methods
      // They use the core notification service for cancellation

      // Cancel all scheduled timers
      for (final timer in _scheduledTimers.values) {
        timer.cancel();
      }
      _scheduledTimers.clear();

      // Clear pending requests
      _pendingRequests.clear();

      // Update state
      final currentState = state.value;
      if (currentState != null) {
        state = AsyncValue.data(currentState.copyWith(pendingNotifications: const [], lastUpdate: DateTime.now()));
      }

      AppLogger.info('✅ All notifications cancelled');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to cancel all notifications', e, stackTrace);
    }
  }

  /// Get pending notifications
  ///
  /// Returns list of all pending notifications across all services.
  Future<List<ScheduledNotificationInfo>> getPendingNotifications() async {
    if (_isDisposed) return [];

    try {
      final allPending = <ScheduledNotificationInfo>[];

      // Get pending notifications from core notification service
      // Other services don't have individual getPendingNotifications methods
      if (_notificationService != null) {
        final corePending = await _notificationService!.getPendingNotifications();
        // Convert PendingNotificationRequest to ScheduledNotificationInfo
        for (final pending in corePending) {
          allPending.add(
            ScheduledNotificationInfo(
              id: pending.id,
              title: pending.title ?? 'Unknown',
              body: pending.body ?? 'No content',
              scheduledDate: DateTime.now(), // PendingNotificationRequest doesn't have scheduledDate
              channelKey: NotificationChannelKey.general,
              payload: pending.payload != null
                  ? NotificationPayload(
                      id: pending.id,
                      title: pending.title ?? 'Unknown',
                      body: pending.body ?? 'No content',
                      channelKey: NotificationChannelKey.general,
                      timestamp: DateTime.now(),
                      payload: pending.payload,
                    )
                  : null,
              createdAt: DateTime.now(),
            ),
          );
        }
      }

      return allPending;
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to get pending notifications', e, stackTrace);
      return [];
    }
  }

  /// Enable/disable notifications globally
  ///
  /// Controls the global notification state across all services.
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_isDisposed) return;

    try {
      AppLogger.info('🔄 Setting notifications enabled: $enabled');

      if (!enabled) {
        // Cancel all notifications when disabling
        await cancelAllNotifications();
      }

      // Update state
      final currentState = state.value;
      if (currentState != null) {
        state = AsyncValue.data(currentState.copyWith(isEnabled: enabled, lastUpdate: DateTime.now()));
      }

      AppLogger.info('✅ Notifications enabled state updated: $enabled');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to set notifications enabled', e, stackTrace);
    }
  }

  /// Get notification analytics
  ///
  /// Returns current analytics data for all notification operations.
  NotificationAnalytics getAnalytics() {
    final currentState = state.value;
    return currentState?.analytics ?? NotificationAnalytics.initial();
  }

  /// Helper method to convert NotificationType to NotificationChannelKey
  ///
  /// Maps notification types to their corresponding channel keys for analytics.
  NotificationChannelKey _getChannelKeyFromType(NotificationType type) {
    switch (type) {
      case NotificationType.prayer:
        return NotificationChannelKey.prayerTimes;
      case NotificationType.sync:
        return NotificationChannelKey.backgroundSync;
      case NotificationType.systemAlert:
        return NotificationChannelKey.systemAlerts;
      default:
        return NotificationChannelKey.general;
    }
  }

  /// Validate notification request
  ///
  /// Performs comprehensive validation of notification request data.
  ValidationResult _validateRequest(UnifiedNotificationRequest request) {
    final errors = <String>[];

    // Validate ID
    if (request.id < 0 || request.id > 2147483647) {
      errors.add('Notification ID must be between 0 and 2147483647');
    }

    // Validate title
    if (request.title.isEmpty) {
      errors.add('Notification title cannot be empty');
    }

    if (request.title.length > 100) {
      errors.add('Notification title must be 100 characters or less');
    }

    // Validate body
    if (request.body.isEmpty) {
      errors.add('Notification body cannot be empty');
    }

    if (request.body.length > 500) {
      errors.add('Notification body must be 500 characters or less');
    }

    // Validate scheduled date
    if (request.scheduledDate?.isBefore(DateTime.now()) == true) {
      errors.add('Cannot schedule notifications in the past');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Monitor service health
  ///
  /// Monitors individual service health and updates state accordingly
  /// following Context7 MCP single responsibility principle.
  void _monitorServiceHealth(String serviceName, bool Function() healthCheck) {
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }

      final isHealthy = healthCheck();
      final currentState = state.value;
      if (currentState != null) {
        final updatedStatus = Map<String, bool>.from(currentState.serviceStatus);
        updatedStatus[serviceName] = isHealthy;

        state = AsyncValue.data(currentState.copyWith(serviceStatus: updatedStatus, lastUpdate: DateTime.now()));
      }

      if (!isHealthy) {
        AppLogger.warning('⚠️ Service health check failed: $serviceName');
      }
    });
  }

  /// Perform resource cleanup
  ///
  /// Performs periodic resource cleanup following Context7 MCP patterns
  /// for optimal memory management and performance.
  void _performResourceCleanup() {
    try {
      // Clear expired pending requests
      _pendingRequests.removeWhere((request) {
        final isExpired =
            request.scheduledDate != null &&
            request.scheduledDate!.isBefore(DateTime.now().subtract(const Duration(hours: 1)));
        if (isExpired) {
          AppLogger.debug('🧹 Removing expired pending request: ${request.id}');
        }
        return isExpired;
      });

      // Clear old scheduled timers
      final expiredTimers = <int>[];
      _scheduledTimers.forEach((id, timer) {
        if (!timer.isActive) {
          expiredTimers.add(id);
        }
      });

      for (final id in expiredTimers) {
        _scheduledTimers.remove(id);
        AppLogger.debug('🧹 Removed expired timer: $id');
      }

      AppLogger.debug('🧹 Resource cleanup completed');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to perform resource cleanup', e, stackTrace);
    }
  }

  /// Dispose all services
  ///
  /// Disposes all services following Context7 MCP patterns with proper
  /// error handling and resource cleanup.
  Future<void> _disposeAllServices() async {
    final disposalTasks = <Future<void>>[];

    // Dispose notification service
    if (_notificationService != null) {
      disposalTasks.add(
        _disposeService('notification_service', () async {
          await _notificationService!.dispose();
          _notificationService = null;
        }),
      );
    }

    // Dispose prayer service
    if (_prayerService != null) {
      disposalTasks.add(
        _disposeService('prayer_service', () async {
          await _prayerService!.dispose();
          _prayerService = null;
        }),
      );
    }

    // Dispose sync service
    if (_syncService != null) {
      disposalTasks.add(
        _disposeService('sync_service', () async {
          await _syncService!.dispose();
          _syncService = null;
        }),
      );
    }

    // Dispose alert service
    if (_alertService != null) {
      disposalTasks.add(
        _disposeService('alert_service', () async {
          await _alertService!.dispose();
          _alertService = null;
        }),
      );
    }

    // Dispose scheduler
    if (_scheduler != null) {
      disposalTasks.add(
        _disposeService('scheduler', () async {
          await _scheduler!.dispose();
          _scheduler = null;
        }),
      );
    }

    // Dispose analytics service
    if (_analyticsService != null) {
      disposalTasks.add(
        _disposeService('analytics_service', () async {
          await _analyticsService!.dispose();
          _analyticsService = null;
        }),
      );
    }

    // Wait for all disposal tasks to complete
    await Future.wait(disposalTasks);

    // Clear all collections
    _pendingRequests.clear();
    for (var timer in _scheduledTimers.values) {
      timer.cancel();
    }
    _scheduledTimers.clear();

    AppLogger.info('✅ All services disposed successfully');
  }

  /// Dispose individual service
  ///
  /// Disposes an individual service with proper error handling
  /// following Context7 MCP patterns.
  Future<void> _disposeService(String serviceName, Future<void> Function() disposalFunction) async {
    try {
      AppLogger.debug('🧹 Disposing $serviceName');
      await disposalFunction();
      AppLogger.debug('✅ $serviceName disposed successfully');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to dispose $serviceName', e, stackTrace);
    }
  }

  /// Dispose of all resources
  ///
  /// Cleans up all services, timers, and resources.
  void _dispose() {
    if (_isDisposed) return;

    AppLogger.info('🧹 Disposing Unified Notification Manager');

    _isDisposed = true;

    // Cancel batch processing timer
    _batchProcessingTimer?.cancel();

    // Cancel all scheduled timers
    for (final timer in _scheduledTimers.values) {
      timer.cancel();
    }
    _scheduledTimers.clear();

    // Clear pending requests
    _pendingRequests.clear();

    // Dispose services
    _notificationService?.dispose();
    _prayerService?.dispose();
    _syncService?.dispose();
    _alertService?.dispose();
    // Note: NotificationChannelManager doesn't have dispose method
    _scheduler?.dispose();
    _analyticsService?.dispose();

    AppLogger.info('✅ Unified Notification Manager disposed');
  }
}

/// Validation Result
///
/// Result of notification request validation.
class ValidationResult {
  /// Whether the validation passed
  final bool isValid;

  /// List of validation error messages
  final List<String> errors;

  /// Creates a validation result
  const ValidationResult({required this.isValid, required this.errors});
}
